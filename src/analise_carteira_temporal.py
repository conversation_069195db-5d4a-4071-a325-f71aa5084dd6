#!/usr/bin/env python3
"""
Análise Temporal da Carteira de Investimentos

Este script gera análise temporal da carteira com:
- Gráficos temporais individuais para cada ação
- Gráfico temporal do rendimento total da carteira
- Evolução do valor investido vs valor atual ao longo do tempo

Baseado na estrutura do analise_carteira_simples.py mas com foco temporal.
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

# Configurar estilo dos gráficos
plt.style.use('default')
sns.set_palette("husl")

def corrigir_valores_tims3(dados, ticker):
    """Aplica correção específica para TIMS3 até 02/07/2025"""
    if ticker != 'TIMS3.SA':
        return dados

    # Data limite para correção (timezone-naive)
    data_limite = pd.to_datetime('2025-07-02').tz_localize(None)
    colunas_preco = ['Open', 'High', 'Low', 'Close', 'Adj Close']

    # Verificar se o índice é datetime
    if not isinstance(dados.index, pd.DatetimeIndex):
        return dados

    # Garantir que o índice seja timezone-naive
    dados_index = dados.index
    if dados_index.tz is not None:
        dados_index = dados_index.tz_convert(None)
        dados.index = dados_index

    # Filtrar dados até a data limite
    mask_correcao = dados_index <= data_limite

    if mask_correcao.any():
        print(f"     🔧 Corrigindo valores TIMS3 até 02/07/2025 (dividindo por 100)")

        # Aplicar correção nas colunas de preço
        for coluna in colunas_preco:
            if coluna in dados.columns:
                dados.loc[mask_correcao, coluna] = dados.loc[mask_correcao, coluna] / 100

        print(f"     ✅ Correção aplicada em {mask_correcao.sum()} registros")

    return dados

def carregar_carteira(arquivo_csv):
    """Carrega dados da carteira do arquivo CSV"""
    carteira = pd.read_csv(arquivo_csv)
    carteira['data_compra'] = pd.to_datetime(carteira['data_compra'])
    return carteira

def obter_dados_historicos(tickers, data_inicio):
    """Obtém dados históricos das ações desde a data de início"""
    print("Obtendo dados históricos...")
    dados_historicos = {}
    
    # Calcular data final (hoje + margem)
    data_fim = datetime.now() + timedelta(days=1)
    
    for ticker in tickers:
        try:
            print(f"  Baixando dados para {ticker}...")
            stock = yf.Ticker(ticker)
            hist = stock.history(start=data_inicio, end=data_fim)
            
            if not hist.empty:
                # Aplicar correção para TIMS3 se necessário
                hist = corrigir_valores_tims3(hist, ticker)
                dados_historicos[ticker] = hist
                print(f"  ✓ {len(hist)} registros obtidos para {ticker}")
            else:
                print(f"  ✗ Nenhum dado obtido para {ticker}")
                
        except Exception as e:
            print(f"  ✗ Erro ao obter dados para {ticker}: {e}")
    
    return dados_historicos

def processar_transacoes_temporais(carteira):
    """Processa transações da carteira organizadas temporalmente"""
    # Ordenar por data
    carteira_ordenada = carteira.sort_values('data_compra').copy()
    
    # Criar estrutura para acompanhar posições ao longo do tempo
    transacoes = []
    
    for _, transacao in carteira_ordenada.iterrows():
        ticker = transacao['ticker']
        quantidade = transacao['quantidade']
        data = transacao['data_compra']
        preco = transacao['preco_compra']
        
        transacoes.append({
            'ticker': ticker,
            'data': data,
            'quantidade': quantidade,
            'preco': preco,
            'valor_transacao': quantidade * preco,
            'tipo': 'COMPRA' if quantidade > 0 else 'VENDA'
        })
    
    return pd.DataFrame(transacoes)

def calcular_posicoes_temporais(transacoes_df, dados_historicos):
    """Calcula evolução das posições ao longo do tempo"""
    # Obter todas as datas únicas dos dados históricos
    todas_datas = set()
    for ticker, dados in dados_historicos.items():
        # Converter índice para timezone-naive se necessário
        if dados.index.tz is not None:
            dados.index = dados.index.tz_convert(None)
        todas_datas.update(dados.index.date)

    todas_datas = sorted(todas_datas)

    # Converter para datetime timezone-naive
    todas_datas = [pd.to_datetime(data).tz_localize(None) for data in todas_datas]

    # Filtrar apenas datas a partir da primeira transação
    data_inicio = transacoes_df['data'].min()
    if data_inicio.tz is not None:
        data_inicio = data_inicio.tz_localize(None)
    todas_datas = [data for data in todas_datas if data >= data_inicio]
    
    # Estrutura para armazenar evolução temporal
    evolucao_temporal = []
    
    # Para cada data, calcular posições e valores
    for data_atual in todas_datas:
        # Filtrar transações até a data atual
        transacoes_ate_data = transacoes_df[transacoes_df['data'] <= data_atual]
        
        if transacoes_ate_data.empty:
            continue
        
        # Calcular posições consolidadas até esta data
        posicoes_data = {}
        valor_total_investido = 0
        valor_total_atual = 0
        
        # Consolidar posições por ticker
        for ticker in transacoes_ate_data['ticker'].unique():
            transacoes_ticker = transacoes_ate_data[transacoes_ate_data['ticker'] == ticker]
            
            quantidade_total = transacoes_ticker['quantidade'].sum()
            valor_investido_bruto = transacoes_ticker[transacoes_ticker['quantidade'] > 0]['valor_transacao'].sum()
            valor_vendido = abs(transacoes_ticker[transacoes_ticker['quantidade'] < 0]['valor_transacao'].sum())
            valor_investido_liquido = valor_investido_bruto - valor_vendido
            
            # Obter preço atual para esta data
            preco_atual = None
            if ticker in dados_historicos:
                dados_ticker = dados_historicos[ticker].copy()
                # Garantir que o índice seja timezone-naive
                if dados_ticker.index.tz is not None:
                    dados_ticker.index = dados_ticker.index.tz_convert(None)

                # Garantir que data_atual seja timezone-naive
                data_atual_naive = data_atual
                if hasattr(data_atual, 'tz') and data_atual.tz is not None:
                    data_atual_naive = data_atual.tz_localize(None)

                # Encontrar preço mais próximo da data atual
                datas_disponiveis = dados_ticker.index[dados_ticker.index <= data_atual_naive]
                if not datas_disponiveis.empty:
                    data_mais_proxima = datas_disponiveis.max()
                    preco_atual = dados_ticker.loc[data_mais_proxima, 'Close']
            
            if preco_atual is not None and quantidade_total > 0:
                valor_atual_ticker = quantidade_total * preco_atual
                
                posicoes_data[ticker] = {
                    'quantidade': quantidade_total,
                    'valor_investido_bruto': valor_investido_bruto,
                    'valor_investido_liquido': valor_investido_liquido,
                    'valor_atual': valor_atual_ticker,
                    'preco_atual': preco_atual
                }
                
                valor_total_investido += valor_investido_liquido
                valor_total_atual += valor_atual_ticker
        
        # Calcular rendimento total
        if valor_total_investido > 0:
            rendimento_absoluto = valor_total_atual - valor_total_investido
            rendimento_percentual = (rendimento_absoluto / valor_total_investido) * 100
        else:
            rendimento_absoluto = 0
            rendimento_percentual = 0
        
        evolucao_temporal.append({
            'data': data_atual,
            'valor_investido_total': valor_total_investido,
            'valor_atual_total': valor_total_atual,
            'rendimento_absoluto': rendimento_absoluto,
            'rendimento_percentual': rendimento_percentual,
            'posicoes': posicoes_data.copy()
        })
    
    return evolucao_temporal

def gerar_graficos_temporais_individuais(evolucao_temporal, dados_historicos):
    """Gera gráficos temporais individuais para cada ação"""
    print("Gerando gráficos temporais individuais...")
    
    # Obter todos os tickers únicos
    todos_tickers = set()
    for snapshot in evolucao_temporal:
        todos_tickers.update(snapshot['posicoes'].keys())
    
    # Criar diretório para gráficos individuais
    os.makedirs('results/figures/temporal_individual', exist_ok=True)
    
    for ticker in todos_tickers:
        # Coletar dados temporais para este ticker
        datas = []
        valores_investidos = []
        valores_atuais = []
        quantidades = []
        precos = []
        
        for snapshot in evolucao_temporal:
            if ticker in snapshot['posicoes']:
                posicao = snapshot['posicoes'][ticker]
                datas.append(snapshot['data'])
                valores_investidos.append(posicao['valor_investido_liquido'])
                valores_atuais.append(posicao['valor_atual'])
                quantidades.append(posicao['quantidade'])
                precos.append(posicao['preco_atual'])
        
        if not datas:
            continue
        
        # Criar gráfico para este ticker
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # Gráfico 1: Evolução do valor investido vs atual
        ax1.plot(datas, valores_investidos, label='Valor Investido', linewidth=2, color='blue')
        ax1.plot(datas, valores_atuais, label='Valor Atual', linewidth=2, color='green')
        ax1.fill_between(datas, valores_investidos, valores_atuais, alpha=0.3, 
                        color='green' if valores_atuais[-1] > valores_investidos[-1] else 'red')
        
        ax1.set_title(f'{ticker.replace(".SA", "")} - Evolução do Investimento\n'
                     f'Quantidade: {quantidades[-1]} ações', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Valor (R$)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # Gráfico 2: Evolução do preço da ação
        if ticker in dados_historicos:
            dados_ticker = dados_historicos[ticker]
            # Filtrar dados para o período relevante
            dados_periodo = dados_ticker[dados_ticker.index >= datas[0]]
            
            ax2.plot(dados_periodo.index, dados_periodo['Close'], 
                    label='Preço de Fechamento', linewidth=1.5, color='orange')
            
            # Marcar pontos de compra/venda
            # Aqui você pode adicionar marcadores para transações específicas
            
        ax2.set_title(f'Evolução do Preço da Ação', fontsize=12)
        ax2.set_xlabel('Data', fontsize=12)
        ax2.set_ylabel('Preço (R$)', fontsize=12)
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # Salvar gráfico
        nome_arquivo = f'temporal_{ticker.replace(".SA", "")}.png'
        caminho_arquivo = f'results/figures/temporal_individual/{nome_arquivo}'
        plt.savefig(caminho_arquivo, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  ✓ Gráfico salvo: {caminho_arquivo}")

def gerar_grafico_carteira_total(evolucao_temporal):
    """Gera gráfico da evolução temporal da carteira total"""
    print("Gerando gráfico da carteira total...")
    
    # Extrair dados temporais
    datas = [snapshot['data'] for snapshot in evolucao_temporal]
    valores_investidos = [snapshot['valor_investido_total'] for snapshot in evolucao_temporal]
    valores_atuais = [snapshot['valor_atual_total'] for snapshot in evolucao_temporal]
    rendimentos_pct = [snapshot['rendimento_percentual'] for snapshot in evolucao_temporal]
    
    # Criar gráfico
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # Gráfico 1: Evolução dos valores
    ax1.plot(datas, valores_investidos, label='Valor Total Investido', 
             linewidth=2.5, color='blue', marker='o', markersize=3)
    ax1.plot(datas, valores_atuais, label='Valor Atual da Carteira', 
             linewidth=2.5, color='green', marker='s', markersize=3)
    
    # Preencher área entre as linhas
    ax1.fill_between(datas, valores_investidos, valores_atuais, alpha=0.3,
                    color='green' if valores_atuais[-1] > valores_investidos[-1] else 'red')
    
    ax1.set_title('Evolução Temporal da Carteira de Investimentos', 
                 fontsize=16, fontweight='bold')
    ax1.set_ylabel('Valor (R$)', fontsize=12)
    ax1.legend(loc='upper left', fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # Adicionar anotações de valores finais
    valor_final_investido = valores_investidos[-1]
    valor_final_atual = valores_atuais[-1]
    rendimento_final = valor_final_atual - valor_final_investido
    
    ax1.annotate(f'Investido: R$ {valor_final_investido:.2f}', 
                xy=(datas[-1], valor_final_investido), 
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.7),
                color='white', fontweight='bold')
    
    ax1.annotate(f'Atual: R$ {valor_final_atual:.2f}', 
                xy=(datas[-1], valor_final_atual), 
                xytext=(10, -20), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.7),
                color='white', fontweight='bold')
    
    # Gráfico 2: Evolução do rendimento percentual
    cores_rendimento = ['green' if r >= 0 else 'red' for r in rendimentos_pct]
    ax2.plot(datas, rendimentos_pct, linewidth=2.5, color='purple', marker='D', markersize=4)
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.fill_between(datas, 0, rendimentos_pct, alpha=0.3,
                    color='green' if rendimentos_pct[-1] >= 0 else 'red')
    
    ax2.set_title('Evolução do Rendimento Percentual', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Data', fontsize=12)
    ax2.set_ylabel('Rendimento (%)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)
    
    # Adicionar anotação do rendimento final
    rendimento_final_pct = rendimentos_pct[-1]
    ax2.annotate(f'{rendimento_final_pct:.2f}%', 
                xy=(datas[-1], rendimento_final_pct), 
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', 
                         facecolor='green' if rendimento_final_pct >= 0 else 'red', alpha=0.7),
                color='white', fontweight='bold')
    
    plt.tight_layout()
    
    # Salvar gráfico
    caminho_arquivo = 'results/figures/evolucao_carteira_temporal.png'
    plt.savefig(caminho_arquivo, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"  ✓ Gráfico da carteira total salvo: {caminho_arquivo}")
    
    # Imprimir resumo final
    print(f"\n{'='*60}")
    print(f"RESUMO FINAL DA ANÁLISE TEMPORAL")
    print(f"{'='*60}")
    print(f"Período analisado: {datas[0].strftime('%d/%m/%Y')} a {datas[-1].strftime('%d/%m/%Y')}")
    print(f"Valor total investido: R$ {valor_final_investido:.2f}")
    print(f"Valor atual da carteira: R$ {valor_final_atual:.2f}")
    print(f"Rendimento absoluto: R$ {rendimento_final:.2f}")
    print(f"Rendimento percentual: {rendimento_final_pct:.2f}%")
    
    status = "📈 LUCRO" if rendimento_final >= 0 else "📉 PREJUÍZO"
    print(f"Status: {status}")

def main():
    """Função principal"""
    arquivo_carteira = 'carteira.csv'
    
    print("="*60)
    print("ANÁLISE TEMPORAL DA CARTEIRA DE INVESTIMENTOS")
    print("="*60)
    
    # Carregar carteira
    print("1. Carregando dados da carteira...")
    carteira = carregar_carteira(arquivo_carteira)
    if carteira is None or carteira.empty:
        print("❌ Erro: Não foi possível carregar a carteira")
        return
    
    print(f"   ✓ Carteira carregada: {len(carteira)} transações")
    
    # Processar transações temporalmente
    print("2. Processando transações temporais...")
    transacoes_df = processar_transacoes_temporais(carteira)
    print(f"   ✓ {len(transacoes_df)} transações processadas")
    
    # Obter dados históricos
    print("3. Obtendo dados históricos...")
    tickers = carteira['ticker'].unique()
    data_inicio = carteira['data_compra'].min() - timedelta(days=1)
    dados_historicos = obter_dados_historicos(tickers, data_inicio)
    
    if not dados_historicos:
        print("❌ Erro: Nenhum dado histórico foi obtido")
        return
    
    print(f"   ✓ Dados históricos obtidos para {len(dados_historicos)} tickers")
    
    # Calcular evolução temporal das posições
    print("4. Calculando evolução temporal das posições...")
    evolucao_temporal = calcular_posicoes_temporais(transacoes_df, dados_historicos)
    print(f"   ✓ Evolução calculada para {len(evolucao_temporal)} períodos")
    
    # Criar diretório para resultados
    os.makedirs('results/figures', exist_ok=True)
    
    # Gerar gráficos individuais
    print("5. Gerando gráficos temporais individuais...")
    gerar_graficos_temporais_individuais(evolucao_temporal, dados_historicos)
    
    # Gerar gráfico da carteira total
    print("6. Gerando gráfico da carteira total...")
    gerar_grafico_carteira_total(evolucao_temporal)
    
    # Salvar dados da evolução temporal
    print("7. Salvando dados da evolução temporal...")
    os.makedirs('results', exist_ok=True)
    
    # Converter evolução temporal para DataFrame
    dados_evolucao = []
    for snapshot in evolucao_temporal:
        dados_evolucao.append({
            'data': snapshot['data'],
            'valor_investido_total': snapshot['valor_investido_total'],
            'valor_atual_total': snapshot['valor_atual_total'],
            'rendimento_absoluto': snapshot['rendimento_absoluto'],
            'rendimento_percentual': snapshot['rendimento_percentual']
        })
    
    df_evolucao = pd.DataFrame(dados_evolucao)
    df_evolucao.to_csv('results/evolucao_carteira_temporal.csv', index=False)
    print(f"   ✓ Dados salvos em 'results/evolucao_carteira_temporal.csv'")
    
    print("\n✅ Análise temporal concluída com sucesso!")
    print(f"📁 Gráficos salvos em: results/figures/")
    print(f"📊 Gráficos individuais em: results/figures/temporal_individual/")

if __name__ == "__main__":
    main()
