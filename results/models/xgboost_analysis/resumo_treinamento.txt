RESUMO DO TREINAMENTO XGBOOST - SINAIS DE TRADING
============================================================

Data de treinamento: 2025-07-08 20:08:29

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 5y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (13):
   1. Media_OHLC_Lag_1
   2. Media_OHLC_Lag_2
   3. Media_OHLC_Lag_3
   4. Media_OHLC_Lag_4
   5. Media_OHLC_Lag_5
   6. Media_OHLC_Lag_6
   7. Media_OHLC_Lag_7
   8. Media_OHLC_Lag_8
   9. Media_OHLC_Lag_9
  10. Media_OHLC_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade

RESULTADOS DO MODELO:
  • A<PERSON>r<PERSON>cia Sinais de Compra: 0.507
  • Acurácia Sinais de Venda: 0.511

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
